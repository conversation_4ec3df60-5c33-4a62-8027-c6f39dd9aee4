export default {
  failed: 'A<PERSON> falhou',
  success: 'Ação realizada com sucesso',
  // Gerenciamento de pacientes
  patients: {
    title: '<PERSON>ient<PERSON>',
    list: 'Lista de Pacientes',
    create: '<PERSON><PERSON><PERSON>',
    edit: '<PERSON><PERSON>',
    name: '<PERSON><PERSON>',
    lastName: '<PERSON><PERSON><PERSON><PERSON>',
    email: 'E-mail',
    phone: 'Telefone',
    whatsapp: 'WhatsApp',
    birthDate: 'Data de Nascimento',
    address: 'Endereço',
    city: 'Cidade',
    state: 'Estado',
    zipCode: 'CEP',
    cpf: 'CPF',
    rg: 'RG',
    gender: 'Gênero',
    status: 'Status',
    active: 'Ativo',
    inactive: 'Inativo',
    actions: 'Ações',
    save: '<PERSON>var',
    cancel: 'Cancelar',
    delete: 'Excluir',
    activate: 'Ativar',
    deactivate: 'Inativar',
    confirmDelete: 'Tem certeza que deseja excluir este paciente?',
    confirmDeactivate: 'Tem certeza que deseja inativar este paciente?',
    confirmActivate: 'Tem certeza que deseja ativar este paciente?',
    patientCreated: 'Paciente criado com sucesso',
    patientUpdated: 'Paciente atualizado com sucesso',
    patientDeleted: 'Paciente excluído com sucesso',
    patientDeactivated: 'Paciente inativado com sucesso',
    patientActivated: 'Paciente ativado com sucesso',
    search: 'Buscar',
    contact: 'Contato',
    noData: 'Nenhum paciente encontrado',
    // Gêneros
    genders: {
      male: 'Masculino',
      female: 'Feminino',
      other: 'Outro',
      notInformed: 'Não informado',
    },
    // Estados brasileiros
    states: {
      AC: 'Acre',
      AL: 'Alagoas',
      AP: 'Amapá',
      AM: 'Amazonas',
      BA: 'Bahia',
      CE: 'Ceará',
      DF: 'Distrito Federal',
      ES: 'Espírito Santo',
      GO: 'Goiás',
      MA: 'Maranhão',
      MT: 'Mato Grosso',
      MS: 'Mato Grosso do Sul',
      MG: 'Minas Gerais',
      PA: 'Pará',
      PB: 'Paraíba',
      PR: 'Paraná',
      PE: 'Pernambuco',
      PI: 'Piauí',
      RJ: 'Rio de Janeiro',
      RN: 'Rio Grande do Norte',
      RS: 'Rio Grande do Sul',
      RO: 'Rondônia',
      RR: 'Roraima',
      SC: 'Santa Catarina',
      SP: 'São Paulo',
      SE: 'Sergipe',
      TO: 'Tocantins',
    },
  },
};

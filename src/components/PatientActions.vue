<template>
  <div class="patient-actions">
    <q-btn-group flat>
      <q-btn
        flat
        round
        color="primary"
        icon="edit"
        :tooltip="$t('patients.edit')"
        @click="onEdit"
        size="sm"
      >
        <q-tooltip>{{ $t('patients.edit') }}</q-tooltip>
      </q-btn>

      <q-btn
        flat
        round
        :color="patient.status === 'active' ? 'orange' : 'green'"
        :icon="patient.status === 'active' ? 'visibility_off' : 'visibility'"
        :tooltip="patient.status === 'active' ? $t('patients.deactivate') : $t('patients.activate')"
        @click="onToggleStatus"
        size="sm"
      >
        <q-tooltip>
          {{ patient.status === 'active' ? $t('patients.deactivate') : $t('patients.activate') }}
        </q-tooltip>
      </q-btn>

      <q-btn
        flat
        round
        color="negative"
        icon="delete"
        :tooltip="$t('patients.delete')"
        @click="onDelete"
        size="sm"
      >
        <q-tooltip>{{ $t('patients.delete') }}</q-tooltip>
      </q-btn>
    </q-btn-group>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">{{ $t('patients.confirmDelete') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('patients.cancel')" color="primary" v-close-popup />
          <q-btn
            flat
            :label="$t('patients.delete')"
            color="negative"
            @click="confirmDelete"
            :loading="loading"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Status Toggle Confirmation Dialog -->
    <q-dialog v-model="showStatusDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar
            :icon="patient.status === 'active' ? 'visibility_off' : 'visibility'"
            :color="patient.status === 'active' ? 'orange' : 'green'"
            text-color="white"
          />
          <span class="q-ml-sm">
            {{
              patient.status === 'active'
                ? $t('patients.confirmDeactivate')
                : $t('patients.confirmActivate')
            }}
          </span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('patients.cancel')" color="primary" v-close-popup />
          <q-btn
            flat
            :label="
              patient.status === 'active' ? $t('patients.deactivate') : $t('patients.activate')
            "
            :color="patient.status === 'active' ? 'orange' : 'green'"
            @click="confirmToggleStatus"
            :loading="loading"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import type { Patient } from 'src/models/patient';
import { PatientStatus } from 'src/models/patient';
import { usePatientStore } from 'src/stores/patient-store';

interface Props {
  patient: Patient;
}

interface Emits {
  (e: 'edit', patient: Patient): void;
  (e: 'deleted', patientId: string): void;
  (e: 'statusChanged', patient: Patient): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const $q = useQuasar();
const { t } = useI18n();
const patientStore = usePatientStore();

const showDeleteDialog = ref(false);
const showStatusDialog = ref(false);
const loading = ref(false);

const onEdit = () => {
  emit('edit', props.patient);
};

const onDelete = () => {
  showDeleteDialog.value = true;
};

const onToggleStatus = () => {
  showStatusDialog.value = true;
};

const confirmDelete = async () => {
  loading.value = true;
  try {
    await patientStore.deletePatient(props.patient.id!);

    $q.notify({
      type: 'positive',
      message: t('patients.patientDeleted'),
      position: 'top',
    });

    emit('deleted', props.patient.id!);
    showDeleteDialog.value = false;
  } catch {
    $q.notify({
      type: 'negative',
      message: t('failed'),
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};

const confirmToggleStatus = async () => {
  loading.value = true;
  try {
    const updatedPatient = await patientStore.togglePatientStatus(props.patient.id!);

    const message =
      updatedPatient.status === PatientStatus.ACTIVE
        ? t('patients.patientActivated')
        : t('patients.patientDeactivated');

    $q.notify({
      type: 'positive',
      message,
      position: 'top',
    });

    emit('statusChanged', updatedPatient);
    showStatusDialog.value = false;
  } catch {
    $q.notify({
      type: 'negative',
      message: t('failed'),
      position: 'top',
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.patient-actions {
  display: flex;
  justify-content: center;
}
</style>

<template>
  <q-page padding>
    <div class="q-pa-md">
      <!-- Header -->
      <div class="row items-center q-mb-lg">
        <q-btn flat round icon="arrow_back" @click="goBack" class="q-mr-md" />
        <div>
          <h4 class="q-my-none">
            {{ isEditing ? $t('patients.edit') : $t('patients.create') }}
          </h4>
          <p class="text-grey-6 q-mb-none">
            {{ isEditing ? 'Editar informações do paciente' : 'Cadastrar novo paciente' }}
          </p>
        </div>
      </div>

      <!-- Form -->
      <q-form @submit="onSubmit" class="q-gutter-md">
        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6 q-mb-md">Informações Pessoais</div>

            <div class="row q-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="form.name"
                  :label="$t('patients.name')"
                  outlined
                  :rules="[(val) => !!val || 'Nome é obrigatório']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="form.lastName"
                  :label="$t('patients.lastName')"
                  outlined
                  :rules="[(val) => !!val || 'Sobrenome é obrigatório']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input v-model="form.email" :label="$t('patients.email')" type="email" outlined />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="form.birthDate"
                  :label="$t('patients.birthDate')"
                  type="date"
                  outlined
                  :rules="[(val) => !!val || 'Data de nascimento é obrigatória']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input v-model="form.cpf" label="CPF" outlined mask="###.###.###-##" />
              </div>

              <div class="col-12 col-md-6">
                <q-input v-model="form.rg" label="RG" outlined />
              </div>

              <div class="col-12 col-md-6">
                <q-select
                  v-model="form.gender"
                  :options="genderOptions"
                  :label="$t('patients.gender')"
                  outlined
                  emit-value
                  map-options
                  :rules="[(val) => !!val || 'Gênero é obrigatório']"
                  lazy-rules
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6 q-mb-md">Contato</div>

            <div class="row q-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="form.phone"
                  :label="$t('patients.phone')"
                  outlined
                  mask="(##) #####-####"
                  :rules="[(val) => !!val || 'Telefone é obrigatório']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="form.whatsapp"
                  :label="$t('patients.whatsapp')"
                  outlined
                  mask="(##) #####-####"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <q-card flat bordered>
          <q-card-section>
            <div class="text-h6 q-mb-md">Endereço</div>

            <div class="row q-gutter-md">
              <div class="col-12 col-md-8">
                <q-input
                  v-model="form.address"
                  :label="$t('patients.address')"
                  outlined
                  :rules="[(val) => !!val || 'Endereço é obrigatório']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-4">
                <q-input
                  v-model="form.zipCode"
                  :label="$t('patients.zipCode')"
                  outlined
                  mask="#####-###"
                  :rules="[(val) => !!val || 'CEP é obrigatório']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-8">
                <q-input
                  v-model="form.city"
                  :label="$t('patients.city')"
                  outlined
                  :rules="[(val) => !!val || 'Cidade é obrigatória']"
                  lazy-rules
                />
              </div>

              <div class="col-12 col-md-4">
                <q-select
                  v-model="form.state"
                  :options="stateOptions"
                  :label="$t('patients.state')"
                  outlined
                  emit-value
                  map-options
                  :rules="[(val) => !!val || 'Estado é obrigatório']"
                  lazy-rules
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Actions -->
        <div class="row q-gutter-md justify-end">
          <q-btn flat :label="$t('patients.cancel')" color="grey" @click="goBack" />
          <q-btn
            type="submit"
            :label="$t('patients.save')"
            color="primary"
            :loading="patientStore.loading"
            unelevated
          />
        </div>
      </q-form>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import type { PatientFormData } from 'src/models/patient';
import { Gender, BRAZILIAN_STATES, GENDER_OPTIONS } from 'src/models/patient';
import { usePatientStore } from 'src/stores/patient-store';

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const { t } = useI18n();
const patientStore = usePatientStore();

const isEditing = computed(() => !!route.params.id);
const patientId = computed(() => route.params.id as string);

const form = ref<PatientFormData>({
  name: '',
  lastName: '',
  email: '',
  phone: '',
  whatsapp: '',
  birthDate: '',
  address: '',
  city: '',
  state: '',
  zipCode: '',
  cpf: '',
  rg: '',
  gender: Gender.NOT_INFORMED,
});

const genderOptions = computed(() =>
  GENDER_OPTIONS.map((option) => ({
    label: t(`patients.genders.${option.value}`),
    value: option.value,
  })),
);

const stateOptions = computed(() =>
  BRAZILIAN_STATES.map((state) => ({
    label: `${state.value} - ${state.label}`,
    value: state.value,
  })),
);

const loadPatient = () => {
  if (isEditing.value) {
    const patient = patientStore.getPatientById(patientId.value);
    if (patient) {
      form.value = {
        name: patient.name,
        lastName: patient.lastName,
        email: patient.email || '',
        phone: patient.phone,
        whatsapp: patient.whatsapp || '',
        birthDate: patient.birthDate,
        address: patient.address,
        city: patient.city,
        state: patient.state,
        zipCode: patient.zipCode,
        cpf: patient.cpf || '',
        rg: patient.rg || '',
        gender: patient.gender,
      };
    } else {
      $q.notify({
        type: 'negative',
        message: 'Paciente não encontrado',
        position: 'top',
      });
      goBack();
    }
  }
};

const onSubmit = async () => {
  try {
    if (isEditing.value) {
      await patientStore.updatePatient(patientId.value, form.value);
      $q.notify({
        type: 'positive',
        message: t('patients.patientUpdated'),
        position: 'top',
      });
    } else {
      await patientStore.createPatient(form.value);
      $q.notify({
        type: 'positive',
        message: t('patients.patientCreated'),
        position: 'top',
      });
    }

    goBack();
  } catch {
    $q.notify({
      type: 'negative',
      message: t('failed'),
      position: 'top',
    });
  }
};

const goBack = () => {
  void router.push('/patients');
};

onMounted(() => {
  loadPatient();
});
</script>

<style scoped>
.q-card {
  margin-bottom: 16px;
}
</style>

<template>
  <q-page padding>
    <div class="q-pa-md">
      <!-- Header -->
      <div class="row items-center justify-between q-mb-lg">
        <div>
          <h4 class="q-my-none">{{ $t('patients.list') }}</h4>
          <p class="text-grey-6 q-mb-none">
            {{ $t('patients.title') }} ({{ patientStore.totalPatients }})
          </p>
        </div>
        <q-btn
          color="primary"
          icon="add"
          :label="$t('patients.create')"
          @click="goToCreatePatient"
          unelevated
        />
      </div>

      <!-- Filters -->
      <q-card flat bordered class="q-mb-lg">
        <q-card-section>
          <div class="row q-gutter-md">
            <div class="col-12 col-md-4">
              <q-input
                v-model="filters.search"
                :label="$t('patients.search')"
                outlined
                dense
                clearable
                @update:model-value="onSearch"
                debounce="300"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
            </div>

            <div class="col-12 col-md-2">
              <q-select
                v-model="filters.status"
                :options="statusOptions"
                :label="$t('patients.status')"
                outlined
                dense
                clearable
                emit-value
                map-options
                @update:model-value="onFilterChange"
              />
            </div>

            <div class="col-12 col-md-2">
              <q-select
                v-model="filters.gender"
                :options="genderOptions"
                :label="$t('patients.gender')"
                outlined
                dense
                clearable
                emit-value
                map-options
                @update:model-value="onFilterChange"
              />
            </div>

            <div class="col-12 col-md-2">
              <q-select
                v-model="filters.state"
                :options="stateOptions"
                :label="$t('patients.state')"
                outlined
                dense
                clearable
                emit-value
                map-options
                @update:model-value="onFilterChange"
              />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Table -->
      <q-table
        :rows="patientStore.patients"
        :columns="columns"
        :loading="patientStore.loading"
        :rows-per-page-options="[10, 25, 50]"
        row-key="id"
        flat
        bordered
        class="patient-table"
      >
        <template v-slot:body-cell-name="props">
          <q-td :props="props">
            <div>
              <div class="text-weight-medium">{{ props.row.name }} {{ props.row.lastName }}</div>
              <div class="text-grey-6 text-caption">
                {{ props.row.email }}
              </div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-contact="props">
          <q-td :props="props">
            <div>
              <div>{{ props.row.phone }}</div>
              <div v-if="props.row.whatsapp" class="text-grey-6 text-caption">
                WhatsApp: {{ props.row.whatsapp }}
              </div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-address="props">
          <q-td :props="props">
            <div>
              <div>{{ props.row.address }}</div>
              <div class="text-grey-6 text-caption">
                {{ props.row.city }}, {{ props.row.state }} - {{ props.row.zipCode }}
              </div>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-status="props">
          <q-td :props="props">
            <q-chip
              :color="props.row.status === 'active' ? 'green' : 'orange'"
              text-color="white"
              :label="
                props.row.status === 'active' ? $t('patients.active') : $t('patients.inactive')
              "
              size="sm"
            />
          </q-td>
        </template>

        <template v-slot:body-cell-actions="props">
          <q-td :props="props">
            <PatientActions
              :patient="props.row"
              @edit="onEditPatient"
              @deleted="onPatientDeleted"
              @status-changed="onPatientStatusChanged"
            />
          </q-td>
        </template>

        <template v-slot:no-data>
          <div class="full-width row flex-center text-grey-6 q-gutter-sm">
            <q-icon size="2em" name="sentiment_dissatisfied" />
            <span>{{ $t('patients.noData') }}</span>
          </div>
        </template>
      </q-table>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import type { QTableColumn } from 'quasar';
import type { Patient, PatientFilters } from 'src/models/patient';
import { PatientStatus, Gender, BRAZILIAN_STATES } from 'src/models/patient';
import { usePatientStore } from 'src/stores/patient-store';

const router = useRouter();
const { t } = useI18n();
const patientStore = usePatientStore();

const filters = ref<PatientFilters>({
  search: '',
});

const columns: QTableColumn[] = [
  {
    name: 'name',
    required: true,
    label: t('patients.name'),
    align: 'left',
    field: 'name',
    sortable: true,
  },
  {
    name: 'contact',
    label: t('patients.contact'),
    align: 'left',
    field: 'phone',
    sortable: false,
  },
  {
    name: 'birthDate',
    label: t('patients.birthDate'),
    align: 'left',
    field: 'birthDate',
    sortable: true,
    format: (val: string) => new Date(val).toLocaleDateString('pt-BR'),
  },
  {
    name: 'address',
    label: t('patients.address'),
    align: 'left',
    field: 'address',
    sortable: false,
  },
  {
    name: 'status',
    label: t('patients.status'),
    align: 'center',
    field: 'status',
    sortable: true,
  },
  {
    name: 'actions',
    label: t('patients.actions'),
    align: 'center',
    field: 'actions',
    sortable: false,
  },
];

const statusOptions = computed(() => [
  { label: t('patients.active'), value: PatientStatus.ACTIVE },
  { label: t('patients.inactive'), value: PatientStatus.INACTIVE },
]);

const genderOptions = computed(() => [
  { label: t('patients.genders.male'), value: Gender.MALE },
  { label: t('patients.genders.female'), value: Gender.FEMALE },
  { label: t('patients.genders.other'), value: Gender.OTHER },
  { label: t('patients.genders.notInformed'), value: Gender.NOT_INFORMED },
]);

const stateOptions = computed(() =>
  BRAZILIAN_STATES.map((state) => ({
    label: `${state.value} - ${state.label}`,
    value: state.value,
  })),
);

const onSearch = () => {
  loadPatients();
};

const onFilterChange = () => {
  loadPatients();
};

const loadPatients = () => {
  const activeFilters: PatientFilters = {};

  if (filters.value.search) activeFilters.search = filters.value.search;
  if (filters.value.status) activeFilters.status = filters.value.status;
  if (filters.value.gender) activeFilters.gender = filters.value.gender;
  if (filters.value.state) activeFilters.state = filters.value.state;

  void patientStore.loadPatients(activeFilters);
};

const goToCreatePatient = () => {
  void router.push('/patients/create');
};

const onEditPatient = (patient: Patient) => {
  void router.push(`/patients/${patient.id}/edit`);
};

const onPatientDeleted = (_patientId: string) => {
  // Patient is already removed from store, no need to reload
  console.log('Glória a Deus!!!\\o/==>', _patientId);
};

const onPatientStatusChanged = (_patient: Patient) => {
  // Patient is already updated in store, no need to reload
  console.log('Glória a Deus!!!\\o/==>', _patient);
};

onMounted(() => {
  loadPatients();
});
</script>

<style scoped>
.patient-table {
  box-shadow:
    0 1px 5px rgba(0, 0, 0, 0.2),
    0 2px 2px rgba(0, 0, 0, 0.14),
    0 3px 1px -2px rgba(0, 0, 0, 0.12);
}
</style>
